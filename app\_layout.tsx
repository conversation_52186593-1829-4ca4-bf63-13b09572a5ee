import { seedAllData } from "@/lib/seedInterests";
import { useAuthStore } from "@/stores/authStore";
import { Stack } from "expo-router";
import { useEffect } from "react";
import { StatusBar } from "react-native";
import Toast from "react-native-toast-message";
import "./global.css";

export default function RootLayout() {
  const initialize = useAuthStore((state) => state.initialize);

  useEffect(() => {
    // Initialize auth state and seed data on app load
    const initializeApp = async () => {
      try {
        await initialize();
        // Seed interests and relationship preferences
        await seedAllData();
      } catch (error) {
        console.error("Error initializing app:", error);
      }
    };

    initializeApp();
  }, [initialize]);

  return (
    <>
      <StatusBar hidden={false} />
      <Stack>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(root)"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(auth)"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(onboarding)"
          options={{
            headerShown: false,
          }}
        />
      </Stack>
      <Toast
        config={{
          success: (props) => (
            <Toast.BaseToast
              {...props}
              style={{
                borderLeftColor: "#10B981",
                backgroundColor: "#F0FDF4",
                borderWidth: 1,
                borderColor: "#BBF7D0",
              }}
              contentContainerStyle={{ paddingHorizontal: 15 }}
              text1Style={{
                fontSize: 16,
                fontWeight: "600",
                color: "#065F46",
              }}
              text2Style={{
                fontSize: 14,
                color: "#047857",
              }}
            />
          ),
          error: (props) => (
            <Toast.ErrorToast
              {...props}
              style={{
                borderLeftColor: "#EF4444",
                backgroundColor: "#FEF2F2",
                borderWidth: 1,
                borderColor: "#FECACA",
              }}
              contentContainerStyle={{ paddingHorizontal: 15 }}
              text1Style={{
                fontSize: 16,
                fontWeight: "600",
                color: "#991B1B",
              }}
              text2Style={{
                fontSize: 14,
                color: "#DC2626",
              }}
            />
          ),
          info: (props) => (
            <Toast.BaseToast
              {...props}
              style={{
                borderLeftColor: "#EC4899",
                backgroundColor: "#FDF2F8",
                borderWidth: 1,
                borderColor: "#FBCFE8",
              }}
              contentContainerStyle={{ paddingHorizontal: 15 }}
              text1Style={{
                fontSize: 16,
                fontWeight: "600",
                color: "#BE185D",
              }}
              text2Style={{
                fontSize: 14,
                color: "#DB2777",
              }}
            />
          ),
        }}
      />
    </>
  );
}
