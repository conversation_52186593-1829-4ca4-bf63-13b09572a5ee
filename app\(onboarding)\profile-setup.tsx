import { supabase } from "@/lib/supabase";
import { toast } from "@/lib/toast";
import { useAuthStore } from "@/stores/authStore";
import { useOnboardingStore } from "@/stores/onboardingStore";
import { useProfileStore } from "@/stores/profileStore";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const ProfileSetup = () => {
  const { user } = useAuthStore();
  const {
    currentStep,
    onboardingData,
    updateOnboardingData,
    nextStep,
    previousStep,
  } = useOnboardingStore();
  const {
    interests,
    relationshipPreferences,
    fetchInterests,
    fetchRelationshipPreferences,
    updateProfile,
    updateUserInterests,
    updateUserRelationshipPreferences,
    markProfileComplete,
    loading: profileLoading,
  } = useProfileStore();

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchInterests();
    fetchRelationshipPreferences();
  }, []);

  // Ensure profile exists when component mounts
  useEffect(() => {
    const initializeProfile = async () => {
      if (user?.id) {
        // Try to fetch existing profile first
        try {
          const { error } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();

          if (error && error.code === "PGRST116") {
            // Profile doesn't exist, create it
            const { error: createError } = await supabase
              .from("profiles")
              .insert({
                id: user.id,
                username:
                  user.email?.split("@")[0] || `user_${user.id.slice(0, 8)}`,
                is_first_time: true,
                notification_enabled: false,
                profile_complete: false,
                visibility_status: "public",
                last_active_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              });

            if (createError) {
              console.error("Error creating profile:", createError);
            }
          } else if (error) {
            console.error("Error fetching profile:", error);
          }
        } catch (error) {
          console.error("Error in profile initialization:", error);
        }
      }
    };

    initializeProfile();
  }, [user?.id]);

  const handleNext = async () => {
    // Validation logic
    if (currentStep === 1 && !onboardingData.profile.full_name?.trim()) {
      toast.error("Please enter your full name");
      return;
    }

    if (currentStep === 2 && !onboardingData.profile.gender) {
      toast.error("Please select your gender");
      return;
    }

    if (currentStep === 3 && !onboardingData.profile.age) {
      toast.error("Please enter your age");
      return;
    }

    if (currentStep === 4 && onboardingData.interests.length === 0) {
      toast.error("Please select at least one interest");
      return;
    }

    if (
      currentStep === 5 &&
      onboardingData.relationshipPreferences.length === 0
    ) {
      toast.error("Please select at least one relationship preference");
      return;
    }

    // Submit on the last step
    if (currentStep === 6) {
      await handleSubmit();
      return;
    }

    // Save data after each step
    await saveCurrentStepData();
    nextStep();
  };

  const saveCurrentStepData = async () => {
    if (!user?.id) {
      console.error("No user ID available for saving profile data");
      return;
    }

    setIsLoading(true);
    try {
      // Filter out empty/invalid fields to avoid constraint violations
      const profileUpdates: any = {
        id: user.id,
        updated_at: new Date().toISOString(),
        profile_complete: false,
      };

      // Ensure username is set if not already present
      if (onboardingData.profile.username?.trim()) {
        profileUpdates.username = onboardingData.profile.username.trim();
      } else if (user.email) {
        // Generate username from email if not set
        profileUpdates.username = user.email.split("@")[0];
      } else {
        // Fallback username
        profileUpdates.username = `user_${user.id.slice(0, 8)}`;
      }

      // Only include non-empty fields
      if (onboardingData.profile.full_name?.trim()) {
        profileUpdates.full_name = onboardingData.profile.full_name.trim();
      }
      if (onboardingData.profile.gender) {
        profileUpdates.gender = onboardingData.profile.gender;
      }
      if (onboardingData.profile.age) {
        profileUpdates.age = onboardingData.profile.age;
      }
      if (onboardingData.profile.about?.trim()) {
        profileUpdates.about = onboardingData.profile.about.trim();
      }
      if (onboardingData.profile.sexual_orientation) {
        profileUpdates.sexual_orientation =
          onboardingData.profile.sexual_orientation;
      }
      if (onboardingData.profile.looking_for) {
        profileUpdates.looking_for = onboardingData.profile.looking_for;
      }

      const success = await updateProfile(profileUpdates);

      if (!success) {
        throw new Error("Failed to update profile");
      }
    } catch (error: any) {
      console.error("Error saving profile data:", error);
      toast.error(error.message || "Failed to save profile data");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!user?.id) {
      toast.error("User session not found");
      return;
    }

    setIsLoading(true);
    try {
      // Filter out empty/invalid fields for final profile update
      const profileUpdates: any = {
        id: user.id,
        profile_complete: true,
        is_first_time: false,
        last_active_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Ensure username is set if not already present
      if (onboardingData.profile.username?.trim()) {
        profileUpdates.username = onboardingData.profile.username.trim();
      } else if (user.email) {
        // Generate username from email if not set
        profileUpdates.username = user.email.split("@")[0];
      } else {
        // Fallback username
        profileUpdates.username = `user_${user.id.slice(0, 8)}`;
      }

      // Only include non-empty fields
      if (onboardingData.profile.full_name?.trim()) {
        profileUpdates.full_name = onboardingData.profile.full_name.trim();
      }
      if (onboardingData.profile.gender) {
        profileUpdates.gender = onboardingData.profile.gender;
      }
      if (onboardingData.profile.age) {
        profileUpdates.age = onboardingData.profile.age;
      }
      if (onboardingData.profile.about?.trim()) {
        profileUpdates.about = onboardingData.profile.about.trim();
      }
      if (onboardingData.profile.sexual_orientation) {
        profileUpdates.sexual_orientation =
          onboardingData.profile.sexual_orientation;
      }
      if (onboardingData.profile.looking_for) {
        profileUpdates.looking_for = onboardingData.profile.looking_for;
      }

      // Update profile
      const success = await updateProfile(profileUpdates);

      if (!success) {
        throw new Error("Failed to update profile");
      }

      // Save interests and relationship preferences
      await updateUserInterests(onboardingData.interests);
      await updateUserRelationshipPreferences(
        onboardingData.relationshipPreferences
      );

      // Mark profile as complete
      await markProfileComplete();

      // Show success message
      toast.success("Profile created successfully! Welcome to Pyar Wala!");

      // Navigate to main app
      router.replace("/(root)/(tabs)/main");
    } catch (error: any) {
      toast.error(error.message || "Failed to create profile");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleInterest = (interestId: number) => {
    const currentInterests = onboardingData.interests;
    if (currentInterests.includes(interestId)) {
      updateOnboardingData({
        interests: currentInterests.filter((id) => id !== interestId),
      });
    } else {
      if (currentInterests.length >= 10) {
        toast.warning("You can select up to 10 interests");
        return;
      }
      updateOnboardingData({
        interests: [...currentInterests, interestId],
      });
    }
  };

  const toggleRelationshipPreference = (preferenceId: number) => {
    const currentPrefs = onboardingData.relationshipPreferences;
    if (currentPrefs.includes(preferenceId)) {
      updateOnboardingData({
        relationshipPreferences: currentPrefs.filter(
          (id) => id !== preferenceId
        ),
      });
    } else {
      updateOnboardingData({
        relationshipPreferences: [...currentPrefs, preferenceId],
      });
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderNameStep();
      case 2:
        return renderGenderStep();
      case 3:
        return renderAgeStep();
      case 4:
        return renderInterestsStep();
      case 5:
        return renderRelationshipPreferencesStep();
      case 6:
        return renderSummaryStep();
      default:
        return renderNameStep();
    }
  };

  const renderNameStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-8">
        What's your name?
      </Text>

      <TextInput
        className="bg-white p-4 rounded-xl border border-gray-200 text-lg mb-8"
        placeholder="Enter your full name"
        value={onboardingData.profile.full_name || ""}
        onChangeText={(text) =>
          updateOnboardingData({ profile: { full_name: text } })
        }
      />

      <TouchableOpacity
        onPress={handleNext}
        disabled={!onboardingData.profile.full_name?.trim()}
        className={`p-4 rounded-xl ${
          onboardingData.profile.full_name?.trim()
            ? "bg-pink-500"
            : "bg-gray-300"
        }`}
      >
        <Text className="text-white text-lg font-semibold text-center">
          Continue
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderGenderStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-8">I am a</Text>

      <View className="space-y-4 mb-8">
        {["woman", "man", "other"].map((gender) => (
          <TouchableOpacity
            key={gender}
            onPress={() => updateOnboardingData({ profile: { gender } })}
            className={`p-4 rounded-xl flex-row justify-between items-center ${
              onboardingData.profile.gender === gender
                ? "bg-pink-500"
                : "bg-white border border-gray-200"
            }`}
          >
            <Text
              className={`text-lg font-medium ${
                onboardingData.profile.gender === gender
                  ? "text-white"
                  : "text-gray-800"
              }`}
            >
              {gender.charAt(0).toUpperCase() + gender.slice(1)}
            </Text>
            {onboardingData.profile.gender === gender && (
              <Text className="text-white text-xl">✓</Text>
            )}
          </TouchableOpacity>
        ))}
      </View>

      <View className="flex-row space-x-4">
        <TouchableOpacity
          onPress={previousStep}
          className="flex-1 p-4 rounded-xl bg-white border border-pink-500"
        >
          <Text className="text-pink-500 text-lg font-semibold text-center">
            Back
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleNext}
          disabled={!onboardingData.profile.gender}
          className={`flex-1 p-4 rounded-xl ${
            onboardingData.profile.gender ? "bg-pink-500" : "bg-gray-300"
          }`}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAgeStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-8">
        How old are you?
      </Text>

      <TextInput
        className="bg-white p-4 rounded-xl border border-gray-200 text-lg mb-8"
        placeholder="Enter your age"
        keyboardType="numeric"
        value={onboardingData.profile.age?.toString() || ""}
        onChangeText={(text) => {
          // Allow typing any numeric input, including partial numbers
          if (text === "") {
            updateOnboardingData({ profile: { age: null } });
          } else {
            const age = parseInt(text);
            if (!isNaN(age)) {
              updateOnboardingData({ profile: { age } });
            }
          }
        }}
        maxLength={3}
      />

      <Text className="text-gray-600 text-center mb-8">
        You must be 18 or older to use this app
      </Text>

      <View className="flex-row space-x-4">
        <TouchableOpacity
          onPress={previousStep}
          className="flex-1 p-4 rounded-xl bg-white border border-pink-500"
        >
          <Text className="text-pink-500 text-lg font-semibold text-center">
            Back
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleNext}
          disabled={
            !onboardingData.profile.age ||
            onboardingData.profile.age < 18 ||
            onboardingData.profile.age > 100
          }
          className={`flex-1 p-4 rounded-xl ${
            onboardingData.profile.age &&
            onboardingData.profile.age >= 18 &&
            onboardingData.profile.age <= 100
              ? "bg-pink-500"
              : "bg-gray-300"
          }`}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderInterestsStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-4">
        Your interests
      </Text>
      <Text className="text-gray-600 mb-8">Select up to 10 interests</Text>

      <ScrollView className="flex-1 mb-8" showsVerticalScrollIndicator={false}>
        <View className="flex-row flex-wrap gap-3">
          {interests.map((interest) => (
            <TouchableOpacity
              key={interest.id}
              onPress={() => toggleInterest(interest.id)}
              className={`px-4 py-2 rounded-full ${
                onboardingData.interests.includes(interest.id)
                  ? "bg-pink-500"
                  : "bg-white border border-gray-200"
              }`}
            >
              <Text
                className={`${
                  onboardingData.interests.includes(interest.id)
                    ? "text-white"
                    : "text-gray-800"
                }`}
              >
                {interest.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View className="flex-row space-x-4">
        <TouchableOpacity
          onPress={previousStep}
          className="flex-1 p-4 rounded-xl bg-white border border-pink-500"
        >
          <Text className="text-pink-500 text-lg font-semibold text-center">
            Back
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleNext}
          disabled={onboardingData.interests.length === 0}
          className={`flex-1 p-4 rounded-xl ${
            onboardingData.interests.length > 0 ? "bg-pink-500" : "bg-gray-300"
          }`}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderRelationshipPreferencesStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-4">Looking for</Text>
      <Text className="text-gray-600 mb-8">What are you looking for?</Text>

      <ScrollView className="flex-1 mb-8" showsVerticalScrollIndicator={false}>
        <View className="space-y-3">
          {relationshipPreferences.map((pref) => (
            <TouchableOpacity
              key={pref.id}
              onPress={() => toggleRelationshipPreference(pref.id)}
              className={`p-4 rounded-xl flex-row justify-between items-center ${
                onboardingData.relationshipPreferences.includes(pref.id)
                  ? "bg-pink-500"
                  : "bg-white border border-gray-200"
              }`}
            >
              <Text
                className={`text-lg ${
                  onboardingData.relationshipPreferences.includes(pref.id)
                    ? "text-white"
                    : "text-gray-800"
                }`}
              >
                {pref.name}
              </Text>
              {onboardingData.relationshipPreferences.includes(pref.id) && (
                <Text className="text-white text-xl">✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View className="flex-row space-x-4">
        <TouchableOpacity
          onPress={previousStep}
          className="flex-1 p-4 rounded-xl bg-white border border-pink-500"
        >
          <Text className="text-pink-500 text-lg font-semibold text-center">
            Back
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleNext}
          disabled={onboardingData.relationshipPreferences.length === 0}
          className={`flex-1 p-4 rounded-xl ${
            onboardingData.relationshipPreferences.length > 0
              ? "bg-pink-500"
              : "bg-gray-300"
          }`}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderSummaryStep = () => (
    <View className="flex-1 p-6 pt-20 bg-pink-50">
      <Text className="text-4xl font-bold text-gray-800 mb-8">
        Almost done!
      </Text>

      <ScrollView className="flex-1 mb-8" showsVerticalScrollIndicator={false}>
        <View className="bg-white p-6 rounded-xl mb-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Profile Summary
          </Text>

          <View className="space-y-3">
            <View>
              <Text className="text-gray-600">Name</Text>
              <Text className="text-lg font-medium">
                {onboardingData.profile.full_name}
              </Text>
            </View>

            <View>
              <Text className="text-gray-600">Gender</Text>
              <Text className="text-lg font-medium">
                {onboardingData.profile.gender}
              </Text>
            </View>

            <View>
              <Text className="text-gray-600">Age</Text>
              <Text className="text-lg font-medium">
                {onboardingData.profile.age}
              </Text>
            </View>

            <View>
              <Text className="text-gray-600">
                Interests ({onboardingData.interests.length})
              </Text>
              <View className="flex-row flex-wrap gap-2 mt-2">
                {onboardingData.interests.slice(0, 5).map((interestId) => {
                  const interest = interests.find((i) => i.id === interestId);
                  return interest ? (
                    <View
                      key={interest.id}
                      className="bg-pink-100 px-3 py-1 rounded-full"
                    >
                      <Text className="text-pink-700 text-sm">
                        {interest.name}
                      </Text>
                    </View>
                  ) : null;
                })}
                {onboardingData.interests.length > 5 && (
                  <View className="bg-gray-100 px-3 py-1 rounded-full">
                    <Text className="text-gray-600 text-sm">
                      +{onboardingData.interests.length - 5} more
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <View className="flex-row space-x-4">
        <TouchableOpacity
          onPress={previousStep}
          className="flex-1 p-4 rounded-xl bg-white border border-pink-500"
        >
          <Text className="text-pink-500 text-lg font-semibold text-center">
            Back
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleNext}
          className="flex-1 p-4 rounded-xl bg-pink-500"
        >
          <Text className="text-white text-lg font-semibold text-center">
            Complete Profile
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="#fdf2f8" />
      {renderStep()}
      {(isLoading || profileLoading) && (
        <View className="absolute inset-0 bg-black/30 flex items-center justify-center">
          <ActivityIndicator size="large" color="#ec4899" />
        </View>
      )}
    </SafeAreaView>
  );
};

export default ProfileSetup;
