import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import "react-native-url-polyfill/auto";

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || "";
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "";

// Validate credentials to provide better error messages
if (!supabaseUrl || !supabaseAnonKey) {
  console.error(
    "Missing Supabase credentials. Please check your .env configuration."
  );
}

// Create a custom storage implementation that checks for window
const ExpoSecureStorage = {
  getItem: async (key: string) => {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
      return AsyncStorage.getItem(key);
    }
    return null;
  },
  setItem: async (key: string, value: string) => {
    if (typeof window !== "undefined") {
      await AsyncStorage.setItem(key, value);
    }
    return;
  },
  removeItem: async (key: string) => {
    if (typeof window !== "undefined") {
      await AsyncStorage.removeItem(key);
    }
    return;
  },
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: ExpoSecureStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
