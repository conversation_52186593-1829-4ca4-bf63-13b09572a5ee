import { supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import { create } from "zustand";

interface AuthState {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  isProfileComplete: boolean;

  // Actions
  setSession: (session: Session | null) => void;
  signOut: () => Promise<void>;
  initialize: () => Promise<void>;
  checkProfileComplete: () => Promise<void>;
  createProfileIfNeeded: (user: User) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  session: null,
  user: null,
  loading: true,
  isAuthenticated: false,
  isProfileComplete: false,

  setSession: (session) => {
    set({
      session,
      user: session?.user || null,
      isAuthenticated: !!session?.user,
      loading: false,
    });

    // Check profile completion when session is set
    if (session?.user) {
      get().checkProfileComplete();
    }
  },

  signOut: async () => {
    try {
      await supabase.auth.signOut();
      set({
        session: null,
        user: null,
        isAuthenticated: false,
        isProfileComplete: false,
        loading: false,
      });
    } catch (error) {
      console.error("Error signing out:", error);
    }
  },

  checkProfileComplete: async () => {
    const { user } = get();
    if (!user) return;

    try {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("profile_complete")
        .eq("id", user.id)
        .single();

      if (error && error.code !== "PGRST116") {
        console.error("Error checking profile completion:", error);
        return;
      }

      set({ isProfileComplete: profile?.profile_complete || false });
    } catch (error) {
      console.error("Error checking profile completion:", error);
    }
  },

  initialize: async () => {
    set({ loading: true });

    try {
      // Check for existing session
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.error("Error getting session:", error);
        set({ loading: false });
        return;
      }

      // Set session and user
      get().setSession(session);

      // Setup auth state change listener
      supabase.auth.onAuthStateChange(async (event, session) => {
        console.log("Auth state changed:", event, session?.user?.id);

        // Handle new user signup
        if (event === "SIGNED_IN" && session?.user) {
          await get().createProfileIfNeeded(session.user);
        }

        get().setSession(session);
      });
    } catch (error) {
      console.error("Error initializing auth:", error);
      set({ loading: false });
    }
  },

  createProfileIfNeeded: async (user: User) => {
    try {
      // Check if profile already exists
      const { error: fetchError } = await supabase
        .from("profiles")
        .select("id")
        .eq("id", user.id)
        .single();

      // If profile doesn't exist, create it
      if (fetchError && fetchError.code === "PGRST116") {
        const { error: insertError } = await supabase.from("profiles").insert({
          id: user.id,
          username: user.email?.split("@")[0] || `user_${user.id.slice(0, 8)}`,
          is_first_time: true,
          notification_enabled: false,
          profile_complete: false,
          visibility_status: "public",
          last_active_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (insertError) {
          console.error("Error creating profile:", insertError);
        } else {
          console.log("Profile created successfully for user:", user.id);
        }
      }
    } catch (error) {
      console.error("Error in createProfileIfNeeded:", error);
    }
  },
}));
