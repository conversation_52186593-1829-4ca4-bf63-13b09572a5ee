import SignUpCom from "@/components/auth/sign-up";
import { supabase } from "@/lib/supabase";
import { toast } from "@/lib/toast";
import { useAuthStore } from "@/stores/authStore";
import { router } from "expo-router";
import React, { useState } from "react";
import { Platform, View } from "react-native";

const SignUp = () => {
  const [loading, setLoading] = useState(false);
  const { checkProfileComplete } = useAuthStore();

  const createUserProfile = async (userId: string, email: string) => {
    try {
      const { error } = await supabase.from("profiles").insert({
        id: userId,
        username: email.split("@")[0], // Use email prefix as initial username
        is_first_time: true,
        notification_enabled: false,
        profile_complete: false,
        visibility_status: "public",
        last_active_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      if (error) {
        console.error("Error creating profile:", error);
        // Don't throw error here as auth was successful
      }
    } catch (error) {
      console.error("Error in createUserProfile:", error);
    }
  };

  const handleSignUp = async (email: string) => {
    if (!email) {
      toast.error("Please provide your email address");
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          shouldCreateUser: true,
          emailRedirectTo:
            Platform.OS === "web" ? window.location.origin : undefined,
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success(
          "Check your email! We've sent a one-time password to your email address"
        );
        router.push({
          pathname: "/(auth)/verify-user",
          params: { email, isNewUser: "true" },
        });
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to sign up");
      console.error("Sign up error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: string) => {
    if (provider !== "google") return;

    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider as "google",
        options: {
          redirectTo:
            Platform.OS === "web"
              ? window.location.origin
              : "pyarwalaapp://auth",
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        // Profile creation will be handled by auth state change listener
        toast.info("Redirecting to Google...");
        console.log("Google OAuth initiated successfully");
      }
    } catch (error: any) {
      toast.error("Failed to sign up with Google");
      console.error("Google sign up error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="flex-1">
      <SignUpCom
        onSignUp={handleSignUp}
        onSocialLogin={handleSocialLogin}
        loading={loading}
      />
    </View>
  );
};

export default SignUp;
